---
# Process single IP - handles file replacement
# This file is included for each IP. It expects 'current_ip', 'service_files', and 'service_name' from parent.

- name: "Check server connectivity for {{ current_ip }}"
  ansible.builtin.ping:
  delegate_to: "{{ current_ip }}"
  register: ping_result
  failed_when: false

- name: "Skip processing if server unreachable for {{ current_ip }}"
  ansible.builtin.debug:
    msg: "Warning: Server {{ current_ip }} is unreachable. Skipping file processing for this IP."
  when: ping_result.failed | default(false)

- name: "Check sudo permissions for {{ current_ip }}"
  ansible.builtin.command: "sudo -n true" # noqa: command-instead-of-shell
  register: sudo_check_result
  failed_when: false
  changed_when: false
  delegate_to: "{{ current_ip }}"
  when:
    - not (ping_result.failed | default(false))
    - service_sudo | default(false)

- name: "Process files for reachable server {{ current_ip }}"
  when: not (ping_result.failed | default(false))
  block:
    - name: "Display file processing info for {{ current_ip }}"
      ansible.builtin.debug:
        msg: |
          Processing IP: {{ current_ip }} ({{ service_name }})
          ============================================
          Files to process: {{ service_files | join(', ') }} ({{ service_files | length }} files)
          String replacement: {{ mongo_old_string }} → {{ mongo_new_string }}
          Sudo permissions: {{ 'Available' if (sudo_check_result is defined and sudo_check_result.rc == 0) else 'Not available' if service_sudo else 'Not required' }}

    # Process each file on the current IP
    - name: "Process string replacement in each file for {{ current_ip }}"
      ansible.builtin.include_tasks: task-process-single-file.yml
      loop: "{{ service_files }}"
      loop_control:
        loop_var: current_file
        label: "{{ current_ip }}:{{ current_file }}"
      register: file_processing_results

    # Display processing summary for this IP
    - name: "Display processing summary for {{ current_ip }}"
      ansible.builtin.debug:
        msg: |
          Processing Summary for {{ current_ip }} ({{ service_name }})
          =====================================================
          Total files processed: {{ service_files | length }}
          Server status: {{ 'Reachable' if not (ping_result.failed | default(false)) else 'Unreachable' }}
          Sudo permissions: {{ 'Available' if (sudo_check_result is defined and sudo_check_result.rc == 0) else 'Not available' if service_sudo else 'Not required' }}

          Files processed:
          {% for file in service_files %}
          - {{ file }}
          {% endfor %}

          String replacement: {{ mongo_old_string }} → {{ mongo_new_string }}
